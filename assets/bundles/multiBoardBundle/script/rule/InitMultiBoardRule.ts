import BoardComponent from '../../../../advmain/scripts/modules/level/ecs/components/board/BoardComponent';
import SlotComponent from '../../../../advmain/scripts/modules/level/ecs/components/board/SlotComponent';
import NodeComponent from '../../../../advmain/scripts/modules/level/ecs/components/NodeComponent';
import OpacityComponent from '../../../../advmain/scripts/modules/level/ecs/components/OpacityComponent';
import { TempleteType } from '../../../../advmain/scripts/modules/level/ecs/registry/templete/TempleteRegistry';
import { ECSRuleBase, IECSRuleConfig } from '../../../../advmain/scripts/modules/level/ecs/rules/core/ECSRuleBase';
import { ECSEvent } from '../../../../advmain/scripts/modules/level/ecs/GameEvent';
import { BoardTempleteMustArgs, BoardTempleteOptionalArgs } from '../../../../advmain/scripts/modules/level/ecs/template/BoardTemplete';
import { SlotTempleteMustArgs } from '../../../../advmain/scripts/modules/level/ecs/template/SlotTemplete';
import { CellTempleteMustArgs } from '../../../../advmain/scripts/modules/level/ecs/template/CellTemplete';
import { RelationName } from '../../../../advmain/scripts/modules/level/ecs/cores/center/EntityCenter/WorldRelation';
import { IPoint } from '../../../../advmain/scripts/modules/level/ecs/define/EcsDefine';
import { CellColor, CellType, NormalColor7, BoardLayer, CellSize } from '../../../../advmain/scripts/modules/level/ecs/define/BoardDefine';
import { IBoardConfig } from '../../../../advmain/scripts/modules/level/ecs/config/conf/BoardConfig';

// 接口定义
interface IBlockInfo {
    slotEntity: number;
    cellConfigs: ICellConfig[];
    boardCenter: IPoint;
    targetPosition: IPoint;
    boardEntity: number;
    rc: IRowCol;
}

interface ICellConfig {
    color: CellColor;
    type: CellType;
}

interface IRowCol {
    r: number;
    c: number;
}

interface ILayerBlocksData {
    layerIndex: number;
    blocks: IBlockInfo[];
}

interface IAllBlocksData extends IBlockInfo {
    globalIndex: number;
    layerIndex: number;
}

interface IParentSize {
    width: number;
    height: number;
}

interface IRectangle {
    x: number;
    y: number;
    width: number;
    height: number;
}

interface ISlotConfig {
    [key: number]: {
        type: CellType;
        color: CellColor;
    };
}
export interface IInitMultiBoardRuleConfig extends IECSRuleConfig {
    /** 棋盘组合ID */
    boardIdList: string[];
}
/**初始化棋盘规则 只负责创建块 */
export class InitMultiBoardRule extends ECSRuleBase<IInitMultiBoardRuleConfig> {
    canExecute(): boolean {
        // 检查是否已经存在棋盘实体（快照恢复时）
        const existingBoards = this.world.query([BoardComponent]);
        if (existingBoards.length > 0) {
            return false;
        }
        return true;
    }

    execute(): void {
        const world = this.world;
        const boardIdList = this.config.boardIdList;
        const boardConfigs = boardIdList.map((id) => world.configCenter.getBoardConfig(id));
        const parentScene = world.ECSLayerType.SceneLayerEntity;
        // 创建所有棋盘和块，创建完成后发送事件通知动画系统
        this.createAllBoardsAndBlocks(boardConfigs, parentScene, () => {
            world.eventBus.emit(ECSEvent.GameEvent.MULTIBOARD_BLOCKS_CREATED);
        });
    }

    /**
     * 创建所有棋盘和块
     */
    private createAllBoardsAndBlocks(boards: IBoardConfig[], parentScene: number, onComplete: () => void): void {
        // 1. 创建所有层级的棋盘
        const allBoardEntities = this.createAllBoardsFromBottomToTop(boards, parentScene);
        // 2. 收集所有层级的块信息
        const allLayersBlocks = this.collectAllLayersBlocksInfo(boards, allBoardEntities);
        // 3. 批量创建所有块在中心位置
        this.createAllLayersBlocksAtCenter(allLayersBlocks, () => {
            onComplete();
        });
    }

    /**
     * 从底层到顶层创建所有棋盘
     */
    private createAllBoardsFromBottomToTop(boards: IBoardConfig[], parentScene: number): number[] {
        const boardEntities: number[] = [];
        // 从底层到顶层创建棋盘
        for (let i = boards.length - 1; i >= 0; i--) {
            const cfg = boards[i];
            const boardEntity = this.createBoardInstantly(cfg, parentScene, i);
            boardEntities[i] = boardEntity; // 保持原始索引顺序
        }
        return boardEntities;
    }

    /**
     * 收集所有层级的块信息（按层级分组）
     */
    private collectAllLayersBlocksInfo(boards: IBoardConfig[], boardEntities: number[]): ILayerBlocksData[] {
        const allLayersBlocks: ILayerBlocksData[] = [];
        // 从底层到顶层收集块信息
        for (let i = boards.length - 1; i >= 0; i--) {
            const cfg = boards[i];
            const boardEntity = boardEntities[i];
            const layerBlocks = this.collectBlocksToGenerate(cfg, boardEntity);

            if (layerBlocks.length > 0) {
                allLayersBlocks.push({
                    layerIndex: i,
                    blocks: layerBlocks,
                });
            }
        }
        return allLayersBlocks;
    }

    /**
     * 简化版本：创建所有层级的块在中心位置
     */
    private createAllLayersBlocksAtCenter(allLayersBlocks: ILayerBlocksData[], onComplete: () => void): void {
        if (allLayersBlocks.length === 0) {
            onComplete();
            return;
        }
        // 合并所有层级的块信息，用于批量处理
        const allBlocksToCreate: Array<{ blockInfo: IBlockInfo; layerIndex: number }> = [];
        for (const layerData of allLayersBlocks) {
            for (const blockInfo of layerData.blocks) {
                allBlocksToCreate.push({
                    blockInfo,
                    layerIndex: layerData.layerIndex,
                });
            }
        }
        // 使用优化的分批创建
        this.createBlocksInBatchesSimplified(allBlocksToCreate, onComplete);
    }

    /**
     * 简化版本：分批异步创建块，
     */
    private createBlocksInBatchesSimplified(blocksToCreate: Array<{ blockInfo: IBlockInfo; layerIndex: number }>, onComplete: () => void): void {
        const batchSize = 30; // 增加批量大小到30个，减少批次数量
        const batchInterval = 0; // 使用最小延迟，但仍然让出控制权
        let currentIndex = 0;
        let completedBlocks = 0;
        const totalBlocks = blocksToCreate.length;
        const processBatch = () => {
            const endIndex = Math.min(currentIndex + batchSize, totalBlocks);
            let batchCompleted = 0;
            const batchTarget = endIndex - currentIndex;
            // 处理当前批次
            for (let i = currentIndex; i < endIndex; i++) {
                const { blockInfo } = blocksToCreate[i];
                this.createSingleBlockAtCenter(blockInfo, () => {
                    batchCompleted++;
                    completedBlocks++;
                    // 当前批次完成
                    if (batchCompleted === batchTarget) {
                        // 更新索引
                        currentIndex = endIndex;
                        // 检查是否全部完成
                        if (completedBlocks === totalBlocks) {
                            onComplete();
                        } else {
                            requestAnimationFrame(processBatch);
                        }
                    }
                });
            }
        };
        // 开始处理第一批
        processBatch();
    }

    /**
     * 立即创建棋盘（无动画）
     */
    private createBoardInstantly(cfg: IBoardConfig, parentScene: number, index: number): number {
        const boardEntity = this.createBoardEntity(cfg, parentScene, index);
        this.setupBoardProperties(boardEntity, cfg, index);
        this.createSlotStructure(boardEntity, cfg);
        return boardEntity;
    }

    /**
     * 在中心点创建单个块
     */
    private createSingleBlockAtCenter(blockInfo: IBlockInfo, onComplete: () => void): void {
        const { slotEntity, cellConfigs, boardCenter, boardEntity, rc } = blockInfo;
        const slotComp = this.prepareSlotForBlocks(slotEntity);
        if (!slotComp) {
            onComplete();
            return;
        }
        // 在中心位置创建所有cell
        this.createAllCellsAtCenter(cellConfigs, boardEntity, rc, slotComp, boardCenter, () => {
            onComplete();
        });
    }

    /**
     * 在中心位置创建所有cell
     */
    private createAllCellsAtCenter(
        cellConfigs: ICellConfig[],
        boardEntity: number,
        rc: IRowCol,
        slotComp: SlotComponent,
        boardCenter: IPoint,
        onComplete: () => void,
    ): void {
        let completedCells = 0;
        const totalCells = cellConfigs.length;
        for (let i = 0; i < cellConfigs.length; i++) {
            const cellConfig = cellConfigs[i];
            const cellEntity = this.createCellEntity(cellConfig, boardEntity, rc, slotComp.entity);
            const nodeComp = this.world.getComponent(cellEntity, NodeComponent);
            if (nodeComp) {
                nodeComp.x = boardCenter.x;
                nodeComp.y = boardCenter.y;
            }
            completedCells++;
            if (completedCells === totalCells) {
                onComplete();
            }
        }
    }

    /**
     * 创建棋盘实体
     */
    private createBoardEntity(cfg: IBoardConfig, parentScene: number, index: number): number {
        const mustArgs: BoardTempleteMustArgs = {
            rCount: cfg.rows,
            cCount: cfg.cols,
            boardId: cfg.boardId,
            parentEntity: parentScene,
            nodeParam: {
                x: cfg.startPos.x,
                y: cfg.startPos.y,
                scale: cfg.cellSize / CellSize,
            },
        };
        const optionalArgs: Partial<BoardTempleteOptionalArgs> = {
            isCanPut: cfg.isCanPut,
        };
        return this.world.templeteCenter.createTempleteEntity(TempleteType.Board, mustArgs, optionalArgs);
    }

    /**
     * 设置棋盘属性
     */
    private setupBoardProperties(boardEntity: number, cfg: IBoardConfig, index: number): void {
        // 设置zIndex
        this.world.getComponent(boardEntity, NodeComponent).zIndex = 10 - index;
        // 设置棋盘组件属性
        const boardComp = this.world.getComponent(boardEntity, BoardComponent);
        boardComp.boardCellColor = NormalColor7[this.world.fixedMathCenter.randomInt(0,6)];
        boardComp.isShowBoardBg = cfg.isShowBoardBg;
    }

    /**
     * 创建空的slot结构
     */
    private createSlotStructure(boardEntity: number, cfg: IBoardConfig): void {
        const parentSize = { width: cfg.cols, height: cfg.rows };
        const boardComp = this.world.getComponent(boardEntity, BoardComponent);
        for (let r = 0; r < cfg.rows; r++) {
            const row: number[] = [];
            for (let c = 0; c < cfg.cols; c++) {
                const slotEntity = this.createSingleSlot(cfg, r, c, parentSize, boardEntity);
                row.push(slotEntity);
            }
        }
    }

    /**
     * 创建单个slot
     */
    private createSingleSlot(cfg: IBoardConfig, r: number, c: number, parentSize: IParentSize, boardEntity: number): number | null {
        const rcKey = `${r}_${c}`;
        if (cfg.holes?.includes(rcKey)) {
            return null; // 跳过镂空格
        }
        const mustArgs: SlotTempleteMustArgs = {
            rc: { r, c },
            parentSize: parentSize,
            parentEntity: boardEntity,
        };
        const slotEntity = this.world.templeteCenter.createTempleteEntity(TempleteType.Slot, mustArgs);
        this.world.addComponent(slotEntity, OpacityComponent).opacity = cfg.opcacity;
        return slotEntity;
    }

    /**
     * 收集需要生成的块信息
     */
    private collectBlocksToGenerate(cfg: IBoardConfig, boardEntity: number): IBlockInfo[] {
        const blocksToGenerate: IBlockInfo[] = [];
        const boardComp = this.world.getComponent(boardEntity, BoardComponent);
        const boardCenter = this.calculateBoardCenter(cfg);
        this.iterateAllBoardPositions(cfg, boardComp, boardCenter, boardEntity, blocksToGenerate);
        return blocksToGenerate;
    }

    /**
     * 计算棋盘中心位置
     */
    private calculateBoardCenter(cfg: IBoardConfig): IPoint {
        return {
            x: cfg.startPos.x,
            y: cfg.startPos.y,
        };
    }

    /**
     * 遍历棋盘的所有位置（按顺时针方向）
     */
    private iterateAllBoardPositions(
        cfg: IBoardConfig,
        boardComp: BoardComponent,
        boardCenter: IPoint,
        boardEntity: number,
        blocksToGenerate: IBlockInfo[],
    ): void {
        const positions = this.getPositionsInClockwiseOrder(cfg.rows, cfg.cols);
        for (const { r, c } of positions) {
            this.processPosition(cfg, r, c, boardComp, boardCenter, boardEntity, blocksToGenerate);
        }
    }

    /**
     * 获取顺时针方向的位置序列
     */
    private getPositionsInClockwiseOrder(rows: number, cols: number): Array<IRowCol> {
        const positions: Array<IRowCol> = [];
        if (rows === 0 || cols === 0) return positions;
        let top = 0,
            bottom = rows - 1;
        let left = 0,
            right = cols - 1;
        while (top <= bottom && left <= right) {
            // 第一步：从左到右（上边）
            for (let c = left; c <= right; c++) {
                positions.push({ r: top, c });
            }
            top++;
            // 第二步：从上到下（右边）
            for (let r = top; r <= bottom; r++) {
                positions.push({ r, c: right });
            }
            right--;
            // 第三步：从右到左（下边）- 只有当还有行时
            if (top <= bottom) {
                for (let c = right; c >= left; c--) {
                    positions.push({ r: bottom, c });
                }
                bottom--;
            }
            // 第四步：从下到上（左边）- 只有当还有列时
            if (left <= right) {
                for (let r = bottom; r >= top; r--) {
                    positions.push({ r, c: left });
                }
                left++;
            }
        }
        return positions;
    }

    /**
     * 处理单个位置
     */
    private processPosition(
        cfg: IBoardConfig,
        r: number,
        c: number,
        boardComp: BoardComponent,
        boardCenter: IPoint,
        boardEntity: number,
        blocksToGenerate: IBlockInfo[],
    ): void {
        const rcKey = `${r}_${c}`;
        if (this.shouldSkipPosition(cfg, rcKey)) {
            return;
        }
        const slotCfg = cfg.slots?.[rcKey];
        if (!slotCfg) return;
        const cellConfigs = this.collectCellConfigs(slotCfg, boardComp);
        if (cellConfigs.length > 0) {
            const blockInfo = this.createBlockInfo(cfg, r, c, boardComp, boardCenter, boardEntity, cellConfigs);
            blocksToGenerate.push(blockInfo);
        }
    }

    /**
     * 检查是否应该跳过该位置
     */
    private shouldSkipPosition(cfg: IBoardConfig, rcKey: string): boolean {
        return cfg.holes?.includes(rcKey) || false;
    }

    /**
     * 收集当前位置的所有cell配置
     */
    private collectCellConfigs(slotCfg: ISlotConfig, boardComp: BoardComponent): ICellConfig[] {
        const cellConfigs: ICellConfig[] = [];
        for (let i = 0; i < BoardLayer.Data; i++) {
            const cellCfg = slotCfg[i];
            if (cellCfg) {
                cellConfigs.push({
                    color: boardComp.boardCellColor || cellCfg.color,
                    type: cellCfg.type,
                });
            }
        }
        return cellConfigs;
    }

    /**
     * 创建块信息对象
     */
    private createBlockInfo(
        cfg: IBoardConfig,
        r: number,
        c: number,
        boardComp: BoardComponent,
        boardCenter: IPoint,
        boardEntity: number,
        cellConfigs: ICellConfig[],
    ): IBlockInfo {
        const slotEntity = this.world.getTargets(boardComp.entity, RelationName.PARENT_CHILD, `${r}_${c}`)[0];
        const targetPosition = this.calculateCellWorldPosition(cfg, r, c);
        return {
            slotEntity,
            cellConfigs,
            boardCenter,
            targetPosition,
            boardEntity,
            rc: { r, c },
        };
    }

    /**
     * 计算cell的世界坐标位置
     */
    private calculateCellWorldPosition(cfg: IBoardConfig, row: number, col: number): IPoint {
        const cellSize = cfg.cellSize;
        const boardCenterX = cfg.startPos.x;
        const boardCenterY = cfg.startPos.y;

        // 计算格子相对于棋盘中心的偏移
        const offsetX = (col - (cfg.cols - 1) / 2) * cellSize;
        const offsetY = ((cfg.rows - 1) / 2 - row) * cellSize;
        return {
            x: boardCenterX + offsetX,
            y: boardCenterY + offsetY,
        };
    }

    /**
     * 准备slot以接收块
     */
    private prepareSlotForBlocks(slotEntity: number): SlotComponent | null {
        const slotComp = this.world.getComponent(slotEntity, SlotComponent);
        if (!slotComp) {
            return null;
        }
        return slotComp;
    }

    /**
     * 创建cell实体
     */
    private createCellEntity(cellConfig: ICellConfig, boardEntity: number, rc: IRowCol, slotEntity: number): number {
        const boardComp = this.world.getComponent(boardEntity, BoardComponent);

        const mustArgs: CellTempleteMustArgs = {
            rc,
            parentSize: {
                width: boardComp.cCount,
                height: boardComp.rCount,
            },
            parentEntity: boardEntity,
            cellOption: {
                color: cellConfig.color,
                type: cellConfig.type,
                occupy: true,
                through: true,
            },
            slotEntity,
        };
        const cellEntity = this.world.templeteCenter.createTempleteEntity(TempleteType.Cell, mustArgs);
        return cellEntity;
    }
}
