import { BuffBehaviorBase } from '../../behavior/buff/BuffBehaviorBase';
import { EliminatedBuffBehavior } from '../../behavior/buff/EliminatedBuffBehavior';
import { SettleDoneBuffBehavior } from '../../behavior/buff/SettleDoneBuffBehavior';

/**Buff行为配置表名 */
export const enum BuffBehaviorConst {
    /**消除buff */
    EliminatedBuffBehavior = 'EliminatedBuffBehavior',
    /**实体结算完成buff */
    SettleDoneBuffBehavior = 'SettleDoneBuffBehavior',
}

/**Buff行为配置注册表 */
export const BuffBehaviorRegistry: Record<string, clzz<BuffBehaviorBase>> = {
    [BuffBehaviorConst.EliminatedBuffBehavior]: EliminatedBuffBehavior,
    [BuffBehaviorConst.SettleDoneBuffBehavior]: SettleDoneBuffBehavior,
};
