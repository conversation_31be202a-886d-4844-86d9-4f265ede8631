import ShapeComponent from '../../../components/board/ShapeComponent';
import SettleComponent from '../../../components/SettleComponent';
import { RelationName } from '../../../cores/center/EntityCenter/WorldRelation';
import { System } from '../../../cores/System';
import { SettleType } from '../../../define/BoardDefine';
import { ECSEvent } from '../../../GameEvent';

/**回合系统 */
export default class TurnSystem extends System {
    init(): void {
        this.world.eventBus.on(ECSEvent.GameEvent.SETTLE_TURN, this.onSettleTurn, this);
        this.world.eventBus.on(ECSEvent.GameEvent.SETTLE_DONE, this.onSettleDone, this);
        this.world.eventBus.on(ECSEvent.GameEvent.CELL_REMOVED, this.onCellRemoved, this);
    }
    dispose(): void {
        this.world.eventBus.off(ECSEvent.GameEvent.SETTLE_TURN, this.onSettleTurn, this);
        this.world.eventBus.off(ECSEvent.GameEvent.SETTLE_DONE, this.onSettleDone, this);
        this.world.eventBus.off(ECSEvent.GameEvent.CELL_REMOVED, this.onCellRemoved, this);
    }
    update(dt: number): void {}

    onCellRemoved(entity: number): void {
        const settle = this.world.getComponent(entity, SettleComponent);
        if (!settle || !settle.triggerType.includes(SettleType.Removed)) return;

        const turnData = this.world.cacheCenter.turnData;
        if (!turnData.settleList) turnData.settleList = [];
        turnData.settleList.push({ entity, event: ECSEvent.EntityEvent.REMOVE_SETTLE });
    }

    /**发起结算回合 */
    onSettleTurn(): void {
        const world = this.world;
        const caculateData = this.world.cacheCenter.caculateData;
        const turnState = caculateData.turnState;
        if (turnState === 'wait') return;
        else if (turnState === 'ing') {
            caculateData.turnState = 'wait';
            return;
        }
        caculateData.turnState = 'ing';
        const turnData = world.cacheCenter.turnData;
        this.emitSettleList();

        world.query([SettleComponent]).forEach((entity) => {
            if (world.getComponent(world.getSource(entity, RelationName.PARENT_CHILD), ShapeComponent)) return;
            const settle = world.getComponent(entity, SettleComponent);
            if (!settle || !settle.triggerType.includes(SettleType.Auto)) return;
            turnData.count++;
            world.emitEntityEvent(entity, ECSEvent.EntityEvent.AUTO_SETTLE);
        });
        console.log('TurnSystem', '发起结算回合', turnData.count);
        if (!turnData.count) this.onSettleDone();
    }

    /**执行结算列表，返回是否有执行内容 */
    private emitSettleList() {
        const world = this.world;
        const turnData = world.cacheCenter.turnData;
        if (!turnData.settleList?.length) return;

        turnData.settleList.forEach((item) => {
            if (this.checkSettled(item.entity, item.event)) return;
            turnData.count++;
            world.emitEntityEvent(item.entity, item.event);
        });
        console.log('TurnSystem', '执行结算列表', turnData.count);
        if (turnData.count <= 0) return false;
        turnData.settleList.length = 0;
        return true;
    }

    /**检查是否结算过 */
    private checkSettled(entity: number, event: ECSEvent.EntityEvent) {
        const turnData = this.world.cacheCenter.turnData;
        if (turnData.settled[entity]?.includes(event)) return true;
        if (turnData.settled[entity]) turnData.settled[entity].push(event);
        else turnData.settled[entity] = [event];
    }

    /**实体结算完毕 */
    onSettleDone(): void {
        const world = this.world;
        const turnData = world.cacheCenter.turnData;
        turnData.count--;
        console.log('TurnSystem', '实体结算完毕', turnData.count);
        if (turnData.count > 0) return;
        if (turnData.settleList?.length && this.emitSettleList()) return;

        //回合结算完毕，重置回合数据
        world.cacheCenter.turnData = { count: 0, settled: {} };
        console.log('TurnSystem', '回合结算完毕');
        const caculateData = world.cacheCenter.caculateData;
        if (caculateData.turnState === 'wait') {
            //还有要等待的回合，则继续结算
            caculateData.turnState = '';
            console.log('TurnSystem', '继续下个结算回合');
            this.onSettleTurn();
        } else {
            //所有回合结束，结算完毕
            world.cacheCenter.caculateData = {};
            world.eventBus.emit(ECSEvent.GameEvent.ALL_SETTLE_DONE);
            console.log('TurnSystem', '全部结算完毕');
        }
    }
}
