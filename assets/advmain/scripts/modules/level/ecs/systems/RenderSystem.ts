import { RenderComponent } from '../components/RenderComponent';
import { System } from '../cores/System';
import { ECSEvent } from '../GameEvent';
import { IRemoveRenderEventData } from '../GameEventData';

/**渲染系统 */
export class RenderSystem extends System {
    init() {
        this.world.onComponentAdd(RenderComponent, this._onAdd, this);
        this.world.onComponentRemove(RenderComponent, this._onRemove, this);
    }
    dispose(): void {
        this.world.offComponentAdd(RenderComponent, this._onAdd, this);
        this.world.offComponentRemove(RenderComponent, this._onRemove, this);
    }
    update() {}
    private _onAdd(entityId: number) {
        let willDestroy: boolean = this.world.willDestroyEntity(entityId);
        if (willDestroy) {
            return;
        }
        this.world.eventBus.emit(ECSEvent.BaseEvent.RENDER_ADD, entityId);
    }
    private _onRemove(entity: number) {
        const renderComponent = this.world.getComponent(entity, RenderComponent);
        let removeRenderEventData: IRemoveRenderEventData = { entityId: entity, isBreakLink: renderComponent.isBreakLink };
        this.world.eventBus.emit(ECSEvent.BaseEvent.RENDER_REMOVE, removeRenderEventData);
    }
}
