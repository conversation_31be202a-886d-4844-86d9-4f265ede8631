import { Loader } from '../../../../../../base/Loader';
import { SubGameBridge } from '../../../../../../base/SubGameBridge';
import { PrefabConfig } from '../../../../../prefab/PrefabConfig';
import TargetComponent from '../../../components/special/TargetComponent';
import { TargetType } from '../../../define/BoardDefine';
import { ECSEvent } from '../../../GameEvent';
import { ECSViewBase, IECSViewConfig } from '../../ECSViewBase';

export interface IScoreWinViewConfig extends IECSViewConfig {
    readonly nextBtnPath: string;
}

/**分数关卡成功视图 */
export class ScoreWinECSView extends ECSViewBase<IScoreWinViewConfig> {
    protected initView() {
        const targets = this.getTargetList();
        const target = targets.find((t) => t.key === TargetType.Score);
        this.ui.scoreEffectTxt.getComponent(cc.Label).string = `${target.current}`;
        const sp = this.ui.playBtn.getComponent(cc.Sprite);
        if (sp && this.config.nextBtnPath) {
            this.renderWorld.loadRes('', this.config.nextBtnPath).then((texture: cc.Texture2D) => {
                sp.spriteFrame = new cc.SpriteFrame(texture);
            });
        }
        this.resetBtnState();
        this.showAction();
    }
    protected addEvents() {
        this.ui.backBtn.on(cc.Node.EventType.TOUCH_END, this.onClickBack, this);
        this.ui.playBtn.on(cc.Node.EventType.TOUCH_END, this.onClickPlay, this);
    }
    protected removeEvents() {
        this.ui.backBtn.off(cc.Node.EventType.TOUCH_END, this.onClickBack, this);
        this.ui.playBtn.off(cc.Node.EventType.TOUCH_END, this.onClickPlay, this);
    }
    protected disposeView() {}

    resetBtnState() {
        this.ui.backBtn.active = false;
        this.ui.playBtn.active = false;
        this.ui.backBtn.scale = 0;
        this.ui.playBtn.scale = 0;
        // 确保按钮不可交互
        this.ui.backBtn.getComponent(cc.Button).interactable = false;
        this.ui.playBtn.getComponent(cc.Button).interactable = false;
    }

    /**播放动作 */
    showAction(): void {
        // this.ui.boneAni.active = true;
        // this.ui.boneAni.getComponent(dragonBones.ArmatureDisplay).playAnimation(`newAnimation`, 1);
        let obj = { a: 0 };
        const high = parseInt(this.ui.scoreEffectTxt.getComponent(cc.Label).string);
        cc.tween(obj)
            .to(
                0.55,
                { a: high },
                {
                    progress: (start, end, current, ratio) => {
                        const pro = start + (end - start) * ratio;

                        // SoundController.getInstance().playTravelEffect(SOUND_TRAVEL.travel_score_change);
                        // if (pro >= (high*0.5) && !bSound) {
                        //     SoundController.getInstance().playTravelEffect(SOUND_TRAVEL.travel_score_suc);
                        //     bSound = true;
                        // }

                        if (cc.isValid(this.ui.scoreEffectTxt)) {
                            this.ui.scoreEffectTxt.getComponent(cc.Label).string = String(parseInt(pro));
                        }
                    },
                },
            )
            .start();

        // 目标面板弹出音效
        // audioInfo.play(ChapterAudioConfig.travel_win_logo);
        this.ui.scoreEffect.active = true;
        this.ui.scoreEffect.getComponent(dragonBones.ArmatureDisplay).playAnimation('HS_1', 1);
        cc.tween(this.node)
            .delay(0.57)
            .call(() => {
            })
            .delay(0.33)
            .call(() => {
                // this.showOtherTraitAnim();
                this.ui.scoreAni.active = true;
                this.ui.scoreAni.getComponent(dragonBones.ArmatureDisplay).playAnimation(`newAnimation`, 1);
                // 目标面板弹出音效
                // audioInfo.play(ChapterAudioConfig.travel_overui_score_suc);
            })
            .delay(1.1)
            .call(() => {
                this.showBtnAnim();
            })
            .start();
    }

    showBtnAnim() {
        this.ui.backBtn.active = true;
        this.ui.backBtn.scale = 0.6;
        this.ui.playBtn.active = true;
        this.ui.playBtn.scale = 0.6;

        // 按钮出现音效
        // audioInfo.play(AudioConfig.s_btnShow);

        cc.tween(this.ui.backBtn)
            .to(0.13, { scale: 1.1 })
            .to(0.07, { scale: 1 })
            .call(() => {
                this.ui.backBtn.getComponent(cc.Button).interactable = true;
            })
            .start();

        cc.tween(this.ui.playBtn)
            .to(0.13, { scale: 1.1 })
            .to(0.07, { scale: 1 })
            .call(() => {
                this.ui.playBtn.getComponent(cc.Button).interactable = true;
            })
            .start();
    }

    /**点击开始 */
    onClickPlay(event: cc.Event.EventTouch, data: any) {
        this.disposeView();
        this.logicWorld.pushInputCmd({
            event: ECSEvent.GameEvent.GAME_NEXT,
            data: null,
        });
        // 展示通关内容
        // if (this.state.throughAll) {
        //     EventManager.dispatchModuleEvent(new E_ChapterList_Show({}));
        // } else {
        //     EventManager.dispatchModuleEvent(new E_ChapterGameOver_ShowFinish({ gameOverIndex: GAMEOVERTYPE.ScoreWin }));
        // }
    }

    onClickBack(event: cc.Event.EventTouch, data: any) {
        this.renderWorld.destroyECSView(this);
        Loader.showUI(PrefabConfig.ChapterMain);
    }

    getTargetList() {
        const targets = this.logicWorld.getComponent(this.logicWorld.query([TargetComponent])[0], TargetComponent).targets;
        return targets;
    }
}
