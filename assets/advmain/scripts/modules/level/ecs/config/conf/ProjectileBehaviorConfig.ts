import { IMultipleProjectileConfig } from '../../behavior/projectile/MultipleProjectileBehavior';
import { INormalProjectileConfig } from '../../behavior/projectile/NormalProjectileBehavior';
import { IProjectileBehaviorConfig } from '../../behavior/projectile/ProjectileBehaviorBase';
import { ProjectileBehaviorConst } from '../../registry/behavior/ProjectileBehaviorRegistry';
import { ProjectilePathBehaviorConst } from '../../registry/behavior/ProjectilePathBehaviorRegistry';

/**投射物行为配置 */
export const ProjectileConfig: Record<string, IProjectileBehaviorConfig> = {
    projectile_collect_icon: {
        projectileBehaviorId: 'projectile_collect_icon',
        behaviorDesc: '收集图标投射物',
        behaviorType: ProjectileBehaviorConst.NormalProjectileBehavior,
        projectilePathBehaviorType: ProjectilePathBehaviorConst.LinearPathBehavior,
        moveParams: {
            speed: 1000,
        },
        onHitBuffs: [],
    } as INormalProjectileConfig,
    projectile_airplane: {
        projectileBehaviorId: 'projectile_airplane',
        behaviorDesc: '飞机投射物',
        behaviorType: ProjectileBehaviorConst.MultipleProjectileBehavior,
        onHitBuffs: ['buff_eliminated_cell', 'buff_settle_done'],
        hitEcsViewId: 'ecsview_simple_effect',
        pathSegments: [
            {
                pathBehaviorType: ProjectilePathBehaviorConst.PausePathBehavior,
                moveParams: {
                    moveTime: 0.6,
                },
            },
            {
                pathBehaviorType: ProjectilePathBehaviorConst.BezierPathBehavior,
                moveParams: {
                    moveTime: 0.8,
                },
            },
        ],
    } as IMultipleProjectileConfig,
};
