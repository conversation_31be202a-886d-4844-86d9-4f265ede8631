import { IBuffBehaviorConfig } from '../../behavior/buff/BuffBehaviorBase';
import { BuffBehaviorConst } from '../../registry/behavior/BuffBehaviorRegistry';

/**Buff行为配置 */
export const BuffBehaviorConfig: Record<string, IBuffBehaviorConfig> = {
    buff_eliminated_cell: {
        buffBehaviorId: 'buff_eliminated_cell',
        behaviorDesc: '消除块buff',
        behaviorType: BuffBehaviorConst.EliminatedBuffBehavior,
        value: 0b10,
        duration: 0,
        interval: 0,
        stackId: null,
    },
    buff_settle_done: {
        buffBehaviorId: 'buff_settle_done',
        behaviorDesc: '实体结算完成buff',
        behaviorType: BuffBehaviorConst.SettleDoneBuffBehavior,
        duration: 0,
        interval: 0,
        stackId: null,
    },
};
