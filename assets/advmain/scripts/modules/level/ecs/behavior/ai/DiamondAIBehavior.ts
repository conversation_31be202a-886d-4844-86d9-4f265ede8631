import { ECSEvent } from '../../GameEvent';
import { CellAIBehavior } from './CellAIBehavior';

/**钻石块AI */
export class DiamondAIBehavior extends CellAIBehavior {
    bindEntityEvent() {
        const events = super.bindEntityEvent();
        events.push(ECSEvent.EntityEvent.AUTO_SETTLE);
        return events;
    }
    receiveEntityEvent(entityId: number, event: ECSEvent.EntityEvent, eventData: any): void {
        if (event === ECSEvent.EntityEvent.AUTO_SETTLE) {
            this.onSettle(entityId);
            return;
        }
        super.receiveEntityEvent(entityId, event, eventData);
    }

    protected settleDone(): void {}

    onSettle(entityId: number): void {
        //若下方是空位则占位并下落

        //结算完毕
        this.world.eventBus.emit(ECSEvent.GameEvent.SETTLE_DONE);
    }
    updateAI(entityId: number, dt: number): void {}
}
