import { CellComponent } from '../../components/board/CellComponent';
import TargetComponent from '../../components/special/TargetComponent';
import { RelationName } from '../../cores/center/EntityCenter/WorldRelation';
import { TargetType } from '../../define/BoardDefine';
import { ECSEvent } from '../../GameEvent';
import { TempleteType } from '../../registry/templete/TempleteRegistry';
import { CellAIBehavior } from './CellAIBehavior';

/**飞机块AI */
export class PlaneAIBehavior extends CellAIBehavior {
    bindEntityEvent() {
        const events = super.bindEntityEvent();
        events.push(ECSEvent.EntityEvent.REMOVE_SETTLE);
        return events;
    }
    receiveEntityEvent(entityId: number, event: ECSEvent.EntityEvent, eventData: any): void {
        if (event === ECSEvent.EntityEvent.REMOVE_SETTLE) {
            this.onRemoveSettle(entityId);
            return;
        }
        super.receiveEntityEvent(entityId, event, eventData);
    }

    /**移除结算 */
    private onRemoveSettle(entityId: number) {
        const targetEntityId = this.getPlaneTarget();
        const cell = this.world.getComponent(entityId, CellComponent);
        //临时写法，等可以获取相对逻辑坐标后这里应该直接取entityId的坐标
        const position = this.world.utilCenter.boardUtil.getEntityRootPoint(this.world.getSource(entityId, RelationName.SLOT_CELL));
        if (!targetEntityId) {
            this.world.templeteCenter.createTempleteEntity(
                TempleteType.Effect,
                { parentEntity: this.world.ECSLayerType.EffectLayerEntity, ecsViewId: 'ecsview_simple_effect' },
                {
                    nodeParam: { position },
                    renderParam: {
                        data: {
                            aniName: `fly_${cell.temp.color}`,
                            src: 'cell/110/gameplay_spine_plane',
                            bundleName: 'level',
                        },
                    },
                },
            );
            this.world.eventBus.emit(ECSEvent.GameEvent.SETTLE_DONE);
            return;
        }

        const targetPoint = this.world.utilCenter.boardUtil.getEntityRootPoint(targetEntityId);
        const targetCell = this.world.getComponent(targetEntityId, CellComponent);
        let c2x = position.x / 2 + targetPoint.x / 2;
        if (Math.abs(targetCell.c - cell.c) < 2) {
            c2x = targetPoint.x + (targetCell.r > cell.c ? 200 : -200);
        }
        this.world.templeteCenter.createTempleteEntity(
            TempleteType.Bullet,
            {
                parentEntity: this.world.ECSLayerType.EffectLayerEntity,
                ecsViewId: 'ecsview_plane_spine',
                projectileBehaviorId: 'projectile_airplane',
                context: { targetEntityId },
            },
            {
                nodeParam: { position },
                renderParam: { data: cell.temp.color },
                pathPrecomputedData: {
                    ctrlList: [
                        { x: position.x, y: position.y + 0.4 * Math.max(200, Math.abs(targetPoint.y - position.y)) },
                        { x: c2x, y: Math.max(position.y, targetPoint.y) + 0.8 * Math.max(200, Math.abs(targetPoint.y - position.y)) },
                    ],
                    easing: 'quadIn',
                },
                hitRenderParam: { data: { aniName: 'BOOM', src: 'cell/110/gameplay_spine_plane', bundleName: 'level' } },
            },
        );
    }

    /**获取飞机目标 */
    private getPlaneTarget() {
        const world = this.world;
        let planeOrderList: { order: number; entity: number }[] = world.cacheCenter.turnData.planeOrderList;
        if (!planeOrderList) {
            planeOrderList = [];
            let targetSet = new Set<number>();
            const BASE_ORDER = 10;
            //根据目标，设置优先级加成
            const orderAdd: { [k: string]: number } = {};
            const targets = world.utilCenter.boardUtil.getUniqueComponent(TargetComponent).targets;
            for (let i = 0; i < targets.length; i++) {
                if (targets[i].current < targets[i].to && world.utilCenter.cellUtil.isCell(targets[i].key)) orderAdd[targets[i].key] = 100;
            }
            //遍历格子，设置优先级
            const fixedMathCenter = world.fixedMathCenter;
            world.utilCenter.boardUtil.occupyCellEach((cellEntity, boardEntity) => {
                if (targetSet.has(cellEntity)) return;
                let cell = world.getComponent(cellEntity, CellComponent);
                let order = BASE_ORDER + (orderAdd[cell.type] || 0);
                //以钻石为目标时，改为下方普通块
                if (cell.type === TargetType.Diamond) {
                    const slotEntity = world.getTargets(boardEntity, RelationName.PARENT_CHILD, `${cell.r + 1}_${cell.c}`)?.[0];
                    if (!slotEntity) return;
                    cellEntity = world.utilCenter.boardUtil.getSlotOccupy(slotEntity);
                    if (!cellEntity) return;
                    cell = world.getComponent(cellEntity, CellComponent);
                    if (cell.type !== TargetType.Normal) order = BASE_ORDER / 2;
                }
                targetSet.add(cellEntity);
                planeOrderList.push({ order: order + fixedMathCenter.random(0,1), entity: cellEntity });
            });
            //根据优先级排序获得最终目标数据
            planeOrderList.sort((a, b) => a.order - b.order);
            world.cacheCenter.turnData.planeOrderList = planeOrderList;
        }
        return planeOrderList.pop()?.entity;
    }
}
