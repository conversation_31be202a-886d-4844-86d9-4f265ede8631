import { AlgoExecuteAdapterOutputData } from '../../algorithm/list/algoSDK/puzzle/algoPuzzle';
import { AudioId } from './config/conf/EcsAudioConfig';
import { CellColor, CellType, TargetType } from './define/BoardDefine';
import { RcShape } from './define/EcsConfig';
import { IPoint } from './define/EcsDefine';
import { GameEventMap } from './GameEvent';

export type IProduceBlockEnd = AlgoExecuteAdapterOutputData;

export interface ISwithBoardData {
    mainEntity: number;
    mirrorEntity: number;
}

export interface IRemoveRenderEventData {
    entityId: number;
    isBreakLink: boolean;
}

/**创建延时规则数据 */
export interface ICreateDelayRuleData {
    /**规则ID */
    ruleId: string;
    /**延时 */
    delay: number;
    /**事件 */
    event: keyof GameEventMap;
    /**事件数据 */
    eventData: GameEventMap[keyof GameEventMap];
}
export interface IPutBlockInfo {
    x: number;
    y: number;
    shape: (typeof RcShape)[number];
    entityId: number;
    boardId?: string;
}

export interface IPutBlockBackInfo extends IPutBlockInfo {
    /**方块是否成功! */
    isSuccess: boolean;
    /**消除颜色 */
    clearColor?: CellColor;
}

export interface IEliminationInfo {
    /**消除行数据 */
    clearRow: Set<number>;
    /**消除列数据 */
    clearCol: Set<number>;
    /**消除格子数据 */
    clears: number[];
    /**放块数据 */
    putBlockBackInfo: IPutBlockBackInfo;
    /**棋盘实体Id */
    boardEntity: number;
    /**消除区域中心位置 */
    clearAreaCenterPos: IPoint;
}

export interface IComboChangedInfo {
    /**棋盘实体Id */
    boardEntity: number;
    /**连击数 */
    comboCount: number;
}
/**设置方块透明度数据 */
export interface ISetBlockOpacityInfo {
    /**实体ID */
    entityId: number;
    /**透明度 */
    opacity: number;
}

/**设置预览方块数据 */
export interface ISetPreviewBlockInfo {
    /**预览方块实体ID */
    entityId: number;
    /**形状 */
    shape: (typeof RcShape)[number];
}

/**改变方块颜色数据 */
export interface IAlterBlockColorInfo {
    /**实体ID */
    entityId: number;
    /**新颜色 */
    color: CellColor;
    /**新类型 */
    type: CellType;
}

/**播放收集目标特效数据 - 实际调用时只传递type数字 */
export interface IPlayCollectTargetEffectInfo {
    /**收集类型 */
    type: number;
}

/**拖拽方块开始数据 - 实际调用时传递entityId和pos */
export interface IDragBlockStartInfo {
    /**方块实体ID */
    entityId: number;
    /**起始位置 */
    pos: { x: number; y: number };
}

/**拖拽方块移动数据 - 实际调用时传递cc.Vec3位置 */
export interface IDragBlockMoveInfo {
    /**当前位置 */
    position: { x: number; y: number };
}

/** 棋盘数据接口 */
export interface IBoardData {
    /**盘面数据 */
    boardOccupy?: number[][];
    /**棋盘行数 */
    rows?: number;
    /**棋盘列数 */
    cols?: number;
    /**棋盘ID */
    boardId?: string;
    /**算法ID */
    algorithmId?: number;
}
/**添加buff数据 */
export interface IAddBuffInfo {
    /**施法者实体ID */
    casterEntityId: number;
    /**目标实体ID */
    ownerEntityId: number;
    /**buff行为ID */
    buffBehaviorId: string;
}
/**播放音效数据 */
export interface IPlayAudioInfo {
    audioId: AudioId | string;
    options?: Partial<falcon.IAudioInfoOption & { forcePlay?: boolean }>;
}
/**停止音效数据 */
export interface IStopAudioInfo {
    audioId: AudioId | string;
}

/**
 * 全屏震动信息
 */
export interface IFullScreenShakeInfo {
    /** 持续时间（秒） */
    duration: number;
    /** 震动频率 (秒) */
    frequency?: number;
    /** 震动幅度 */
    amplitude: number;
}

export type TargetParam = { key: TargetType; to: number; current: number };
