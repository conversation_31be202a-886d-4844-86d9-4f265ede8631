import { BoardScene } from '../../components/board/BoardScene';
import ProduceBlockListComponent from '../../components/ProduceBlockListComponent';
import { RelationName } from '../../cores/center/EntityCenter/WorldRelation';
import { BoardLayer, CELL_TEMPLATE_PARAM, CellColor, CellType, TargetType } from '../../define/BoardDefine';
import { ShapeMargin } from '../../define/EcsDefine';
import { ECSEvent } from '../../GameEvent';
import { TempleteType } from '../../registry/templete/TempleteRegistry';
import { CellOption, DEFAULT_CELL_RENDER_OPTION } from '../../template/CellTemplete';
import { ECSRuleBase, IECSRuleConfig } from '../core/ECSRuleBase';
/**
 * 生成块创建实体规则
 * 该规则在生成块结束时执行，创建对应的实体和组件
 */
export class ProduceBlockCreateEntityRule extends ECSRuleBase<IECSRuleConfig, ECSEvent.GameEvent.PRODUCE_BLOCK_END> {
    public bindEventData() {
        return ECSEvent.GameEvent.PRODUCE_BLOCK_END;
    }
    canExecute(): boolean {
        const produceBlockLists = this.world.query([ProduceBlockListComponent]);
        if (!produceBlockLists || produceBlockLists.length === 0) {
            console.warn('没有找到 ProduceBlockListComponent 实体，无法执行创建实体规则');
            return false;
        }
        return true;
    }
    execute(): void {
        const world = this.world;
        const sceneEntity = this.world.utilCenter.boardUtil.getSceneEntity();
        // 销毁上一次创建的实体
        const boardSceneComp = world.getComponent(sceneEntity, BoardScene)!;
        if (boardSceneComp.waitPutCells.length > 0) {
            // 销毁所有待放置的实体及其子实体
            for (const shapeEntityId of boardSceneComp.waitPutCells) {
                const childEntities = world.getTargets(shapeEntityId, RelationName.PARENT_CHILD);
                for (const childEntityId of childEntities) {
                    world.destroyEntity(childEntityId);
                }
                world.destroyEntity(shapeEntityId);
            }
            // 清空待放置列表
            boardSceneComp.waitPutCells = [];
        }
        const templeteCenter = world.templeteCenter;
        const produceBlockListEntity = this.world.query([ProduceBlockListComponent])[0];
        const produceBlocks = this.world.getComponent(produceBlockListEntity, ProduceBlockListComponent).produceBlockList;
        for (let i = 0; i < produceBlocks.length; i++) {
            const block = produceBlocks[i];
            const rcShape = block.shape;
            const shapeEntity = templeteCenter.createTempleteEntity(TempleteType.Shape, {
                rcShape,
                x: (i - 1) * ShapeMargin,
                parentEntity: world.ECSLayerType.SceneLayerEntity,
            });
            const color = block.color || CellColor.None;
            block.shape.shape.forEach((rc, index) => {
                const type = block.shape.types && block.shape.types[index] ? block.shape.types[index] : TargetType.Normal;
                const cellOption: CellOption = {
                    color,
                    type: type as CellType,
                };
                //是否需要填充元素层
                let layer = DEFAULT_CELL_RENDER_OPTION.layer;
                //临时写法，实际所有数据都会在生成块规则内添加
                if (type === TargetType.Glass) {
                    const cellParam = CELL_TEMPLATE_PARAM[type];
                    if (cellParam.layer) layer = cellParam.layer;
                    cellOption.hp = cellParam.components[0].defaultArg;
                }

                const slotEntity = templeteCenter.createTempleteEntity(TempleteType.Slot, {
                    rc,
                    parentSize: rcShape,
                    parentEntity: shapeEntity,
                });

                if (layer > BoardLayer.Element) {
                    templeteCenter.createTempleteEntity(TempleteType.Cell, {
                        rc,
                        parentSize: rcShape,
                        parentEntity: shapeEntity,
                        cellOption: { color, type: world.fixedMathCenter.hitRate(0.1) ? TargetType.Plane : TargetType.Normal },
                        slotEntity,
                    });
                }

                templeteCenter.createTempleteEntity(TempleteType.Cell, {
                    rc,
                    parentSize: rcShape,
                    parentEntity: shapeEntity,
                    cellOption,
                    slotEntity,
                });
            });
            let boardSceneComp = world.getComponent(sceneEntity, BoardScene)!;
            boardSceneComp.waitPutCells.push(shapeEntity);
        }
        // 销毁ProduceBlockListComponent实体
        this.world.destroyEntity(produceBlockListEntity);
    }
}
