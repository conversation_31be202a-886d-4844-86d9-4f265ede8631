import { ECSRuleBase, IECSRuleConfig } from './core/ECSRuleBase';
import BoardComponent from '../components/board/BoardComponent';
import { TempleteType } from '../registry/templete/TempleteRegistry';
import { CellSize, NormalColor7, BoardLayer } from '../define/BoardDefine';
export interface IInitBoardRuleConfig extends IECSRuleConfig {
    /** 棋盘组合ID */
    boardIdList: string[];
}
/**初始化棋盘规则 */
export class InitBoardRule extends ECSRuleBase<IInitBoardRuleConfig> {
    canExecute(): boolean {
        return true;
    }
    execute(): void {
        const world = this.world;
        const boardIdList = this.config.boardIdList;
        const boardConfigs = boardIdList.map((id) => world.configCenter.getBoardConfig(id));
        boardConfigs.forEach((cfg) => {
            const boardEntity = world.templeteCenter.createTempleteEntity(
                TempleteType.Board,
                {
                    rCount: cfg.rows,
                    cCount: cfg.cols,
                    boardId: cfg.boardId,
                    parentEntity: world.ECSLayerType.SceneLayerEntity,
                    nodeParam: {
                        x: cfg.startPos.x,
                        y: cfg.startPos.y,
                        scale: cfg.cellSize / CellSize,
                    },
                },
                {
                    isCanPut: cfg.isCanPut,
                },
            );
            const parentSize = { width: cfg.cols, height: cfg.rows };
            const boardComp = world.getComponent(boardEntity, BoardComponent);
            boardComp.boardCellColor = NormalColor7[world.fixedMathCenter.randomInt(0,6)];
            for (let r = 0; r < cfg.rows; r++) {
                const row: number[] = [];
                for (let c = 0; c < cfg.cols; c++) {
                    const rcKey = `${r}_${c}`;
                    if (cfg.holes?.includes(rcKey)) {
                        row.push(null);
                        continue; // 跳过镂空格
                    }
                    const slotCfg = cfg.slots?.[rcKey];
                    const rc = { r, c };
                    const slotEntity = world.templeteCenter.createTempleteEntity(TempleteType.Slot, {
                        rc,
                        parentSize: parentSize,
                        parentEntity: boardEntity,
                    });
                    if (slotCfg) {
                        for (let i = 0; i < BoardLayer.Data; i++) {
                            const cellCfg = slotCfg[i];
                            if (!cellCfg) continue;
                            const cellEntity = world.templeteCenter.createTempleteEntity(TempleteType.Cell, {
                                rc,
                                parentSize: parentSize,
                                parentEntity: boardEntity,
                                cellOption: { color: boardComp.boardCellColor ? boardComp.boardCellColor : cellCfg.color, type: cellCfg.type },
                                slotEntity,
                            });
                        }
                    }
                    row.push(slotEntity);
                }
            }
        });
    }
}
