//游戏内相关的定义写在这里，原则上不可引用其他脚本

/**奖励条件枚举 */
export const enum AwardCondition {
    /**放置在盘面 */
    Put = 1,
    /**发生满行列消除 */
    FullLine,
    /**被移除 */
    Removed,
}

/**类型展开 */
export type Expand<T> = T extends infer O ? { [K in keyof O]: O[K] } : never;

export interface IPoint {
    x: number;
    y: number;
}

export interface RowCol {
    r: number;
    c: number;
}

export interface ISize {
    width: number;
    height: number;
}

export interface IRegularTime<T> {
    interval: number;
    delay: number;
    opt: T;
}

export interface IRCShape {
    shape: { r: number; c: number }[];
    width: number;
    height: number;
}

/**拖快间距 */
export const ShapeMargin = 300;
