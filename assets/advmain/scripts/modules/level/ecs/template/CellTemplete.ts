import { RelationName } from '../cores/center/EntityCenter/WorldRelation';
import { CellComponent } from '../components/board/CellComponent';
import NodeComponent from '../components/NodeComponent';
import { RenderComponent } from '../components/RenderComponent';
import { AIComponent } from '../components/AIComponent';
import ClearComponent from '../components/special/ClearComponent';
import { TempleteBase } from '../cores/center/TempleteCenter';
import AwardComponent from '../components/special/AwardComponent';
import { AIBehaviorId } from '../behavior/ai/AIBehaviorBase';
import { BuffComponent } from '../components/combat/BuffComponent';
import HPComponent from '../components/special/HPComponent';
import { ComponentRegistry } from '../registry/ComponentRegistry';
import { CellType, CellColor, BoardLayer, CELL_TEMPLATE_PARAM, TargetType, CellSize } from '../define/BoardDefine';
import { RowCol, ISize, AwardCondition } from '../define/EcsDefine';
import SettleComponent from '../components/SettleComponent';
import { EntityEventComponent } from '../components/EntityEventComponent';

/**方块参数 */
export type CellOption = { type: CellType; color: CellColor; occupy?: boolean; through?: boolean } & { [k: string]: any };

/**
 * 方块模板必传参数
 */
export interface CellTempleteMustArgs {
    /** 行列坐标 */
    rc: RowCol;
    /** 父容器尺寸 */
    parentSize: ISize;
    /** 父实体ID */
    parentEntity: number;
    /**所属格子实体 */
    slotEntity: number;
    /**方块参数 */
    cellOption: CellOption;
}

/**方块渲染模板可选参数 */
export interface CellRenderTempleteOptionalArgs {
    /** 层级 */
    layer: BoardLayer;
    /** 渲染组件ID */
    ecsViewId: string;
}

/**
 * 方块模板可选参数
 */
export interface CellTempleteOptionalArgs extends CellRenderTempleteOptionalArgs {
    /** AI组件ID */
    aiBehaviorId: AIBehaviorId;
    /** 消除码 */
    clearCode: number;
    /** 奖励条件 */
    triggerList: ConstructorParameters<typeof AwardComponent>[0];
    /**额外组件名及默认参数 */
    components?: { name: keyof typeof ComponentRegistry; defaultArg?: any }[];
}

/**默认渲染块的可选参数 */
export const DEFAULT_CELL_RENDER_OPTION = {
    layer: BoardLayer.Element,
    ecsViewId: 'ecsview_cell',
};

/**默认完整块的可选参数 */
const DEFAULT_CELL_OPTION = {
    ...DEFAULT_CELL_RENDER_OPTION,
    aiBehaviorId: 'ai_cell',
    clearCode: 0b111,
    triggerList: [],
};

/**方块通用渲染模板 */
export class CellRenderTemplate<T extends CellRenderTempleteOptionalArgs> extends TempleteBase<CellTempleteMustArgs, T> {
    getDefaultOptionalArgs(mustArgs: CellTempleteMustArgs): T {
        return Object.assign({}, DEFAULT_CELL_RENDER_OPTION) as T;
    }
    createTemplete(mustArgs: CellTempleteMustArgs, optionalArgs: T): number {
        const world = this.world;
        const { rc, parentSize, parentEntity, cellOption, slotEntity } = mustArgs;
        const { layer, ecsViewId } = optionalArgs;
        const cellEntity = world.createEntity();
        world.addComponent(cellEntity, NodeComponent, {
            x: (-parentSize.width * CellSize) / 2 + CellSize / 2 + rc.c * CellSize,
            y: (parentSize.height * CellSize) / 2 - CellSize / 2 - rc.r * CellSize,
        });
        world.addRelation(parentEntity, cellEntity, RelationName.PARENT_CHILD);
        world.addComponent(cellEntity, CellComponent, cellOption.type, layer, cellOption.color, rc, null, cellOption.occupy, cellOption.through);
        world.utilCenter.boardUtil.addRelationBy(slotEntity, cellEntity, RelationName.SLOT_CELL, (a, b) => {
            const aCell = world.getComponent(a, CellComponent);
            const bCell = world.getComponent(b, CellComponent);
            return aCell.layer - bCell.layer;
        });
        world.addComponent(cellEntity, RenderComponent, ecsViewId, { childrenPaths: [BoardLayer[layer]] });
        return cellEntity;
    }
}

/**方块通用完整模板 */
export class CellTemplete extends CellRenderTemplate<CellTempleteOptionalArgs> {
    getDefaultOptionalArgs(mustArgs: CellTempleteMustArgs): CellTempleteOptionalArgs {
        return Object.assign({}, DEFAULT_CELL_OPTION, CELL_TEMPLATE_PARAM[mustArgs.cellOption.type]) as CellTempleteOptionalArgs;
    }
    createTemplete(mustArgs: CellTempleteMustArgs, optionalArgs: CellTempleteOptionalArgs): number {
        const cellEntity = super.createTemplete(mustArgs, optionalArgs);
        const { aiBehaviorId, clearCode, components, triggerList } = optionalArgs;
        const world = this.world;
        world.addComponent(cellEntity, EntityEventComponent);
        world.addComponent(cellEntity, AIComponent, aiBehaviorId);
        world.addComponent(cellEntity, ClearComponent, clearCode);
        world.addComponent(cellEntity, AwardComponent, [
            { condition: AwardCondition.Put, target: TargetType.Score, change: 1 },
            { condition: AwardCondition.Removed, target: mustArgs.cellOption.type, change: 1 },
            ...triggerList,
        ]);
        if (components) {
            components.forEach((v) => {
                switch (v.name) {
                    case 'HPComponent':
                        world.addComponent(cellEntity, HPComponent, v.defaultArg, mustArgs.cellOption.hp);
                        break;
                    case 'SettleComponent':
                        world.addComponent(cellEntity, SettleComponent, [...v.defaultArg]);
                        break;
                    default:
                        console.warn('未处理的组件:' + v);
                }
            });
        }
        world.addComponent(cellEntity, BuffComponent);

        return cellEntity;
    }
}
