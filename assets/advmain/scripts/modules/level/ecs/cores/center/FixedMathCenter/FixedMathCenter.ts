import { ISerializable } from "../../ISerializable";

/** 确定性计算中心 */
export class FixedMathCenter implements ISerializable {
    /** 随机种子 */
    public seed: number;
    /**初始化种子 */
    constructor(seed: number) {
        this.seed = seed;
    }
    // 采用线性同余生成器
    private originRandom(): number {
        this.seed = (this.seed * 16807) % 2147483647;
        return (this.seed - 1) / 2147483646;
    }
    /**
     * 随机数 带小数点
     * @param min 最小随机数
     * @param max 最大随机数
     */
    random(min: number, max: number): number {
        if (min > max) {
            return min;
        }
        const num = min + this.originRandom() * (max - min);
        return num;
    }
    /**
    * 随机数 整数
    * @param min 最小随机数
    * @param max 最大随机数
    */
    randomInt(min: number, max: number): number {
        const num = this.random(min, max);
        return Math.floor(num);
    }
    /**
    * 从列表随机出一个子列表
    * @param arr 列表
    * @param count 随机出的子列表数量
    */
    randomArr<T>(arr: Array<T>, count: number): Array<T> {
        const reservoir: Array<T> = [];
        reservoir.length = Math.min(count, arr.length);
        for (let i = 0; i < reservoir.length; i++) {
            reservoir[i] = arr[i];
        }
        for (let i = count; i < arr.length; i++) {
            // 随机获得一个[0, i]内的随机整数
            const randomInt = this.randomInt(0, i);
            // 如果随机整数在[0, m-1]范围内，则替换蓄水池中的元素
            if (randomInt < count) {
                reservoir[randomInt] = arr[i];
            }
        }
        return reservoir;
    }
    /**随机出列表中某个子项
     * @param arr 列表
     */
    randomItem<T>(arr: Array<T>) {
        if (!arr) { return; }
        const randomIndex: number = this.randomInt(0, arr.length - 1);
        return arr[randomIndex];
    }
    // 洗牌函数
    shuffle<T>(array: Array<T>, returnNew: boolean = true): Array<T> {
        let newArr = array;
        if (returnNew) {
            newArr = array.slice()  // 不改变原数组，将数组剪切一份给newArr
        }
        for (let i = 0; i < newArr.length; i++) {
            const j = this.randomInt(0, i);
            const temp = newArr[i];
            newArr[i] = newArr[j];
            newArr[j] = temp;
        }
        return newArr
    }
    /**
     * 概率 0- 1 看是否命中
     * @param val 命中率
     */
    hitRate(val: number): boolean {
        return val > this.random(0, 1);
    }
    serialize() {
        return {
            seed: this.seed,
        };
    }
    deserialize(data: any): void {
        this.seed = data.seed;
    }
}