import { SettleType } from '../../define/BoardDefine';
import { ECSEvent } from '../../GameEvent';

/**缓存中心 */
export class CacheCenter {
    /**局数据，todo */
    /**结算数据 */
    caculateData: {
        [k: string]: any;
        /*回合状态 */
        turnState?: '' | 'ing' | 'wait';
    } = {};
    /**回合数据 */
    turnData: {
        [k: string]: any;
        /**当前结算数量 */
        count: number;
        /**已结算数据 */
        settled: { [k: number]: ECSEvent.EntityEvent[] };
        /**结算列表 */
        settleList?: { entity: number; event: ECSEvent.EntityEvent }[];
    } = { count: 0, settled: {} };
    /**连胜信息，todo */
    __winStreakData__: { [k: string]: number } = {};
}
