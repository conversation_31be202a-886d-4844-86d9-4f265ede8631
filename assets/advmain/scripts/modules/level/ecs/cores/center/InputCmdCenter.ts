import { LogicWorld } from '../World';
import { ECSEvent } from '../../GameEvent';
import { ISerializable } from '../ISerializable';
export interface IEventCmdData {
    /**事件 */
    event: ECSEvent.BaseEvent | ECSEvent.GameEvent;
    /**数据 */
    data: any;
}
/**
 * 输入命令中心
 * 负责管理输入命令的添加、执行等逻辑
 */
export class InputCmdCenter implements ISerializable{
    private world: LogicWorld;
    
    /** 输入命令列表 */
    private inputCmdList: IEventCmdData[] = [];
    private recordCmdList: IEventCmdData[] = [];
    constructor(world: LogicWorld) {
        this.world = world;
    }
    /**
     * 添加输入命令
     * @param cmdData 事件命令数据
     */
    pushInputCmd(cmdData: IEventCmdData): void {
        this.inputCmdList.push(cmdData);
        this.recordCmdList.push(cmdData);
        // if(cmdData.event !== ECSEvent.GameEvent.DRAG_BLOCK_MOVE ){
        //     this.recordCmdList.push(cmdData);
        // }
    }
    /**
     * 执行输入命令
     */
    executeInputCmd(): void {
        while (this.inputCmdList.length > 0) {
            const cmdData = this.inputCmdList.shift();
            this.world.eventBus.emit(cmdData.event, cmdData.data);
        }
    }
    serialize() {
        return {
            recordCmdList: this.recordCmdList,
        };
    }
    deserialize(data: any): void {
        this.recordCmdList = data.recordCmdList;
    }
}

